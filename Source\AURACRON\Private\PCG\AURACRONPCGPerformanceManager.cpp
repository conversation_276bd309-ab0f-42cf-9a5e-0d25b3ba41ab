// AURACRONPCGPerformanceManager.cpp
// Sistema de Otimização de Performance para PCG AURACRON - UE 5.6
// Implementação das otimizações de performance usando APIs modernas

#include "PCG/AURACRONPCGPerformanceManager.h"
#include "PCG/AURACRONPCGEnvironment.h"

// Definição do STAT para profiling - UE 5.6
DEFINE_STAT(STAT_PCGLODUpdate);
#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/PlayerController.h"
#include "Camera/CameraComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/LocalPlayer.h"
#include "SceneView.h"
#include "RenderingThread.h"
#include "Stats/Stats.h"
#include "HAL/IConsoleManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/GameViewportClient.h"
#include "Logging/StructuredLog.h"
#include "Engine/StreamableManager.h"
#include "RHI.h"
#include "RHIResources.h"

// Console variables para debugging e tuning
static TAutoConsoleVariable<bool> CVarPCGPerformanceEnabled(
    TEXT("auracron.pcg.performance.enabled"),
    true,
    TEXT("Enable PCG performance optimizations"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarPCGLODUpdateRate(
    TEXT("auracron.pcg.performance.lodupdaterate"),
    0.5f,
    TEXT("LOD update rate in seconds"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarPCGMaxVisibleElements(
    TEXT("auracron.pcg.performance.maxvisible"),
    100,
    TEXT("Maximum visible PCG elements"),
    ECVF_Default
);

static TAutoConsoleVariable<bool> CVarPCGAutoOptimization(
    TEXT("auracron.pcg.performance.autooptimization"),
    true,
    TEXT("Enable automatic performance optimizations"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarPCGLODDistance(
    TEXT("auracron.pcg.performance.loddistance"),
    2000.0f,
    TEXT("Distance for LOD transitions"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarPCGCullingDistance(
    TEXT("auracron.pcg.performance.cullingdistance"),
    4000.0f,
    TEXT("Distance for culling PCG elements"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarPCGTargetFPS(
    TEXT("auracron.pcg.performance.targetfps"),
    60.0f,
    TEXT("Target FPS for automatic optimizations"),
    ECVF_Default
);

AAURACRONPCGPerformanceManager::AAURACRONPCGPerformanceManager()
    : bAutoOptimizationEnabled(true)
    , TargetFPS(60.0f)
    , FPSTolerance(5.0f)
    , CurrentDeviceType(EAURACRONDeviceType::Unknown)
    , bDeviceDetected(false)
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickGroup = TG_PostUpdateWork; // Executar após outros updates
    
    // Configurar replicação
    bReplicates = false; // Performance manager é local
    
    // Reservar espaço para arrays
    RegisteredElements.Reserve(200);
    FPSHistory.Reserve(60); // 1 segundo de histórico a 60 FPS
    
    // Inicializar perfis de dispositivos
    InitializeDeviceProfiles();
}

void AAURACRONPCGPerformanceManager::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar sistema apenas no cliente
    if (GetWorld()->GetNetMode() != NM_DedicatedServer)
    {
        // Detectar tipo de dispositivo
        CurrentDeviceType = DetectDeviceType();
        ApplyDeviceSpecificSettings();
        bDeviceDetected = true;
        
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPerformanceManager::BeginPlay - Device type detected: {0}", (int32)CurrentDeviceType);
        InitializePerformanceSystem();
    }
}

void AAURACRONPCGPerformanceManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Só executar no cliente
    if (GetWorld()->GetNetMode() == NM_DedicatedServer)
    {
        return;
    }
    
    // Verificar se otimizações estão habilitadas
    if (!CVarPCGPerformanceEnabled.GetValueOnGameThread())
    {
        return;
    }
    
    // Atualizar localizações dos viewers
    UpdateViewerLocations();
    
    // Aplicar otimizações automáticas se habilitadas
    if (bAutoOptimizationEnabled)
    {
        ApplyAutoOptimizations();
    }
    
    // Limpar elementos inválidos periodicamente
    static float CleanupTimer = 0.0f;
    CleanupTimer += DeltaTime;
    if (CleanupTimer >= 5.0f) // A cada 5 segundos
    {
        CleanupInvalidElements();
        CleanupTimer = 0.0f;
    }
}

void AAURACRONPCGPerformanceManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(LODUpdateTimer);
        GetWorld()->GetTimerManager().ClearTimer(MetricsTimer);
    }
    
    // Limpar streamable manager
    StreamableManager.RequestAsyncLoad(TArray<FSoftObjectPath>(), FStreamableDelegate());
    
    Super::EndPlay(EndPlayReason);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGPerformanceManager::InitializePerformanceSystem()
{
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGPerformanceManager: Inicializando sistema de performance");
    
    // Configurar timers usando UE 5.6 enhanced timer system
    GetWorld()->GetTimerManager().SetTimer(
        LODUpdateTimer,
        this,
        &AAURACRONPCGPerformanceManager::OnLODUpdateTimer,
        PerformanceConfig.LODUpdateInterval,
        true
    );
    
    GetWorld()->GetTimerManager().SetTimer(
        MetricsTimer,
        this,
        &AAURACRONPCGPerformanceManager::OnMetricsTimer,
        0.1f, // Coletar métricas a cada 100ms
        true
    );
    
    // Aplicar configurações iniciais
    ApplyQualitySettings();
    
    // Encontrar e registrar elementos PCG existentes
    TArray<AActor*> FoundActors;
    
    // Registrar ambientes
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGEnvironment::StaticClass(), FoundActors);
    for (AActor* Actor : FoundActors)
    {
        RegisterPCGElement(Actor);
    }
    
    // Registrar trilhas
    FoundActors.Empty();
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGTrail::StaticClass(), FoundActors);
    for (AActor* Actor : FoundActors)
    {
        RegisterPCGElement(Actor);
    }
    
    // Registrar ilhas
    FoundActors.Empty();
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGIsland::StaticClass(), FoundActors);
    for (AActor* Actor : FoundActors)
    {
        RegisterPCGElement(Actor);
    }
    
    // Registrar Prismal Flow
    FoundActors.Empty();
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGPrismalFlow::StaticClass(), FoundActors);
    for (AActor* Actor : FoundActors)
    {
        RegisterPCGElement(Actor);
    }
    
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGPerformanceManager: Sistema inicializado com {0} elementos registrados",
           RegisteredElements.Num());
}

void AAURACRONPCGPerformanceManager::RegisterPCGElement(AActor* Element)
{
    if (!IsValid(Element))
    {
        return;
    }
    
    // Verificar se já está registrado
    for (const FAURACRONPCGElementPerformanceInfo& Info : RegisteredElements)
    {
        if (Info.Actor.Get() == Element)
        {
            return; // Já registrado
        }
    }
    
    // Criar nova entrada
    FAURACRONPCGElementPerformanceInfo NewInfo;
    NewInfo.Actor = Element;
    NewInfo.CurrentLOD = EAURACRONPCGLODLevel::LOD0_Highest;
    NewInfo.LastUpdateTime = GetWorld()->GetTimeSeconds();
    
    RegisteredElements.Add(NewInfo);
    
    UE_LOGFMT(LogTemp, VeryVerbose, "AURACRONPCGPerformanceManager: Elemento registrado - {0}",
           Element->GetName());
}

void AAURACRONPCGPerformanceManager::UnregisterPCGElement(AActor* Element)
{
    if (!IsValid(Element))
    {
        return;
    }
    
    // Encontrar e remover elemento
    for (int32 i = RegisteredElements.Num() - 1; i >= 0; --i)
    {
        if (RegisteredElements[i].Actor.Get() == Element)
        {
            RegisteredElements.RemoveAt(i);
            UE_LOGFMT(LogTemp, VeryVerbose, "AURACRONPCGPerformanceManager: Elemento desregistrado - {0}",
                   Element->GetName());
            break;
        }
    }
}

void AAURACRONPCGPerformanceManager::ForceUpdateLOD()
{
    UpdateLODLevels();
}

void AAURACRONPCGPerformanceManager::SetPerformanceConfig(const FAURACRONPCGPerformanceConfig& NewConfig)
{
    PerformanceConfig = NewConfig;
    
    // Reconfigurar timer de LOD se necessário
    if (LODUpdateTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(LODUpdateTimer);
        GetWorld()->GetTimerManager().SetTimer(
            LODUpdateTimer,
            this,
            &AAURACRONPCGPerformanceManager::OnLODUpdateTimer,
            PerformanceConfig.LODUpdateInterval,
            true
        );
    }
    
    // Aplicar novas configurações
    ApplyQualitySettings();
    
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGPerformanceManager: Configurações de performance atualizadas");
}

void AAURACRONPCGPerformanceManager::SetAutoOptimizationEnabled(bool bEnabled)
{
    bAutoOptimizationEnabled = bEnabled;
    
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGPerformanceManager: Otimizações automáticas {0}",
           bEnabled ? "habilitadas" : "desabilitadas");
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGPerformanceManager::UpdateLODLevels()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGLODUpdate);
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Atualizar informações de distância e visibilidade
    for (FAURACRONPCGElementPerformanceInfo& ElementInfo : RegisteredElements)
    {
        AActor* Actor = ElementInfo.Actor.Get();
        if (!IsValid(Actor))
        {
            continue;
        }
        
        // Calcular distância ao viewer mais próximo
        FVector ActorLocation = Actor->GetActorLocation();
        float MinDistance = MAX_FLT;
        
        for (const FVector& ViewerLocation : ViewerLocations)
        {
            float Distance = FVector::Dist(ActorLocation, ViewerLocation);
            MinDistance = FMath::Min(MinDistance, Distance);
        }
        
        ElementInfo.DistanceToViewer = MinDistance;
        ElementInfo.LastUpdateTime = CurrentTime;
        
        // Calcular novo nível de LOD
        EAURACRONPCGLODLevel NewLOD = CalculateLODLevel(ElementInfo);
        
        // Aplicar LOD se mudou
        if (NewLOD != ElementInfo.CurrentLOD)
        {
            ApplyLODToElement(ElementInfo, NewLOD);
        }
        
        // Atualizar prioridade de renderização
        ElementInfo.RenderPriority = CalculateRenderPriority(ElementInfo);
    }
    
    // Realizar culling
    PerformCulling();
}

EAURACRONPCGLODLevel AAURACRONPCGPerformanceManager::CalculateLODLevel(const FAURACRONPCGElementPerformanceInfo& ElementInfo)
{
    float EffectiveDistance = CalculateEffectiveDistance(ElementInfo);
    
    // Usar UE 5.6 enhanced LOD calculation
    for (int32 i = 0; i < PerformanceConfig.LODDistances.Num(); ++i)
    {
        if (EffectiveDistance <= PerformanceConfig.LODDistances[i])
        {
            return static_cast<EAURACRONPCGLODLevel>(i);
        }
    }
    
    // Se está além de todas as distâncias, cullar
    return EAURACRONPCGLODLevel::LOD4_Culled;
}

void AAURACRONPCGPerformanceManager::ApplyLODToElement(FAURACRONPCGElementPerformanceInfo& ElementInfo, EAURACRONPCGLODLevel NewLOD)
{
    AActor* Actor = ElementInfo.Actor.Get();
    if (!IsValid(Actor))
    {
        return;
    }
    
    ElementInfo.CurrentLOD = NewLOD;
    
    // Aplicar LOD usando UE 5.6 modern component management
    TArray<UPrimitiveComponent*> PrimitiveComponents;
    Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
    
    for (UPrimitiveComponent* Component : PrimitiveComponents)
    {
        if (!IsValid(Component))
        {
            continue;
        }
        
        switch (NewLOD)
        {
        case EAURACRONPCGLODLevel::LOD0_Highest:
            Component->SetVisibility(true);
            Component->SetCastShadow(true);
            Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            break;
            
        case EAURACRONPCGLODLevel::LOD1_High:
            Component->SetVisibility(true);
            Component->SetCastShadow(true);
            Component->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            break;
            
        case EAURACRONPCGLODLevel::LOD2_Medium:
            Component->SetVisibility(true);
            Component->SetCastShadow(false);
            Component->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            break;
            
        case EAURACRONPCGLODLevel::LOD3_Low:
            Component->SetVisibility(true);
            Component->SetCastShadow(false);
            Component->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            break;
            
        case EAURACRONPCGLODLevel::LOD4_Culled:
            Component->SetVisibility(false);
            Component->SetCastShadow(false);
            Component->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            break;
        }
    }
    
    // Aplicar LOD específico para diferentes tipos de atores
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
    {
        // Ajustar escala de atividade baseada no LOD
        float ActivityScale = 1.0f;
        switch (NewLOD)
        {
        case EAURACRONPCGLODLevel::LOD0_Highest: ActivityScale = 1.0f; break;
        case EAURACRONPCGLODLevel::LOD1_High: ActivityScale = 0.8f; break;
        case EAURACRONPCGLODLevel::LOD2_Medium: ActivityScale = 0.6f; break;
        case EAURACRONPCGLODLevel::LOD3_Low: ActivityScale = 0.4f; break;
        case EAURACRONPCGLODLevel::LOD4_Culled: ActivityScale = 0.0f; break;
        }
        
        Environment->SetActivityScale(ActivityScale);
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGPerformanceManager::OnLODUpdateTimer()
{
    // Callback para timer de LOD usando APIs modernas do UE 5.6
    SCOPE_CYCLE_COUNTER(STAT_PCGLODUpdate);

    if (!CVarPCGPerformanceEnabled.GetValueOnGameThread())
    {
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::OnLODUpdateTimer - Updating LOD levels");

    // Atualizar níveis de LOD para todos os elementos registrados
    UpdateLODLevels();

    // Realizar culling se necessário
    PerformCulling();

    // Limpar elementos inválidos
    CleanupInvalidElements();

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::OnLODUpdateTimer - LOD update completed");
}

void AAURACRONPCGPerformanceManager::OnMetricsTimer()
{
    // Callback para timer de métricas usando APIs modernas do UE 5.6
    if (!CVarPCGPerformanceEnabled.GetValueOnGameThread())
    {
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::OnMetricsTimer - Collecting performance metrics");

    // Coletar métricas de performance
    CollectPerformanceMetrics();

    // Aplicar otimizações automáticas baseadas nas métricas
    ApplyAutoOptimizations();

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::OnMetricsTimer - Metrics collection completed");
}

void AAURACRONPCGPerformanceManager::PerformCulling()
{
    // Realizar culling de elementos usando APIs modernas do UE 5.6
    if (!CVarPCGPerformanceEnabled.GetValueOnGameThread())
    {
        return;
    }

    int32 CulledCount = 0;

    for (FAURACRONPCGElementPerformanceInfo& ElementInfo : RegisteredElements)
    {
        // Verificar se elemento deve ser culled baseado na distância e importância
        float EffectiveDistance = CalculateEffectiveDistance(ElementInfo);
        float CullingDistance = CVarPCGCullingDistance.GetValueOnGameThread();

        bool bShouldBeCulled = EffectiveDistance > CullingDistance;

        if (bShouldBeCulled && ElementInfo.CurrentLOD != EAURACRONPCGLODLevel::LOD4_Culled)
        {
            ElementInfo.CurrentLOD = EAURACRONPCGLODLevel::LOD4_Culled;
            CulledCount++;

            // Desativar elemento se possível
            if (ElementInfo.Actor.IsValid() && IsValid(ElementInfo.Actor.Get()))
            {
                if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(ElementInfo.Actor.Get()))
                {
                    Environment->SetActivityScale(0.0f);
                }
            }
        }
        else if (!bShouldBeCulled && ElementInfo.CurrentLOD == EAURACRONPCGLODLevel::LOD4_Culled)
        {
            // Reativar elemento
            ElementInfo.CurrentLOD = EAURACRONPCGLODLevel::LOD3_Low; // Começar com LOD baixo

            if (ElementInfo.Actor.IsValid() && IsValid(ElementInfo.Actor.Get()))
            {
                if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(ElementInfo.Actor.Get()))
                {
                    Environment->SetActivityScale(0.4f); // LOD baixo
                }
            }
        }
    }

    if (CulledCount > 0)
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::PerformCulling - Culled {0} elements", CulledCount);
    }
}

void AAURACRONPCGPerformanceManager::UpdateViewerLocations()
{
    // Atualizar localizações dos viewers usando APIs modernas do UE 5.6
    ViewerLocations.Empty();

    if (UWorld* World = GetWorld())
    {
        // Obter todos os player controllers
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (APawn* Pawn = PC->GetPawn())
                {
                    FVector ViewerLocation = Pawn->GetActorLocation();
                    ViewerLocations.Add(ViewerLocation);
                }
            }
        }
    }

    // Se não há viewers, usar localização padrão
    if (ViewerLocations.Num() == 0)
    {
        ViewerLocations.Add(FVector::ZeroVector);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::UpdateViewerLocations - Updated {0} viewer locations", ViewerLocations.Num());
}

void AAURACRONPCGPerformanceManager::ApplyAutoOptimizations()
{
    // Aplicar otimizações automáticas usando APIs modernas do UE 5.6
    if (!CVarPCGAutoOptimization.GetValueOnGameThread())
    {
        return;
    }

    // Verificar métricas de performance atuais
    float CurrentFPS = 1.0f / GetWorld()->GetDeltaSeconds();
    float LocalTargetFPS = CVarPCGTargetFPS.GetValueOnGameThread();

    if (CurrentFPS < LocalTargetFPS * 0.8f) // Se FPS está 20% abaixo do target
    {
        // Aplicar otimizações agressivas
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPerformanceManager::ApplyAutoOptimizations - Applying aggressive optimizations (FPS: {0}, Target: {1})", CurrentFPS, LocalTargetFPS);

        // Reduzir qualidade geral
        for (FAURACRONPCGElementPerformanceInfo& ElementInfo : RegisteredElements)
        {
            // Reduzir LOD em um nível
            if (ElementInfo.CurrentLOD < EAURACRONPCGLODLevel::LOD3_Low)
            {
                ElementInfo.CurrentLOD = static_cast<EAURACRONPCGLODLevel>(static_cast<int32>(ElementInfo.CurrentLOD) + 1);

                // Aplicar novo LOD
                if (ElementInfo.Actor.IsValid() && IsValid(ElementInfo.Actor.Get()))
                {
                    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(ElementInfo.Actor.Get()))
                    {
                        float ActivityScale = 1.0f - (static_cast<int32>(ElementInfo.CurrentLOD) * 0.2f);
                        Environment->SetActivityScale(FMath::Max(0.0f, ActivityScale));
                    }
                }
            }
        }
    }
    else if (CurrentFPS > LocalTargetFPS * 1.2f) // Se FPS está 20% acima do target
    {
        // Melhorar qualidade gradualmente
        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::ApplyAutoOptimizations - Improving quality (FPS: {0}, Target: {1})", CurrentFPS, LocalTargetFPS);

        // Melhorar LOD gradualmente
        for (FAURACRONPCGElementPerformanceInfo& ElementInfo : RegisteredElements)
        {
            // Melhorar LOD em um nível
            if (ElementInfo.CurrentLOD > EAURACRONPCGLODLevel::LOD0_Highest)
            {
                ElementInfo.CurrentLOD = static_cast<EAURACRONPCGLODLevel>(static_cast<int32>(ElementInfo.CurrentLOD) - 1);

                // Aplicar novo LOD
                if (ElementInfo.Actor.IsValid() && IsValid(ElementInfo.Actor.Get()))
                {
                    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(ElementInfo.Actor.Get()))
                    {
                        float ActivityScale = 1.0f - (static_cast<int32>(ElementInfo.CurrentLOD) * 0.2f);
                        Environment->SetActivityScale(FMath::Max(0.2f, ActivityScale));
                    }
                }
            }
        }
    }
}

float AAURACRONPCGPerformanceManager::CalculateRenderPriority(const FAURACRONPCGElementPerformanceInfo& ElementInfo)
{
    // Calcular prioridade de renderização usando algoritmos modernos do UE 5.6
    float Priority = 1.0f;

    // Fator de distância (mais próximo = maior prioridade)
    float DistanceFactor = 1.0f;
    if (ElementInfo.DistanceToViewer > 0.0f)
    {
        DistanceFactor = 1.0f / (1.0f + ElementInfo.DistanceToViewer * 0.001f); // Normalizar distância
    }

    // Fator de importância (baseado no tipo de elemento) - Alinhado com AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md
    float ImportanceFactor = 1.0f;
    if (ElementInfo.Actor.IsValid() && IsValid(ElementInfo.Actor.Get()))
    {
        if (Cast<AAURACRONPCGEnvironment>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 1.5f; // Ambientes (Planície Radiante, Firmamento Zephyr, Reino Purgatório)
        }
        else if (Cast<AAURACRONPCGIsland>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 2.0f; // Ilha Central Auracron com setores Nexus/Santuário/Arsenal/Caos
        }
        else if (Cast<AAURACRONPCGPrismalFlow>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 1.8f; // Fluxo Prismal - núcleo serpentino central
        }
        else if (Cast<AAURACRONPCGTrail>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 1.6f; // Trilhos Solar/Axis/Lunar dinâmicos
        }
    }

    // Fator de performance (elementos com melhor performance têm prioridade)
    float PerformanceFactor = 1.0f;
    if (ElementInfo.LastFrameTime > 0.0f)
    {
        PerformanceFactor = 1.0f / (1.0f + ElementInfo.LastFrameTime * 1000.0f); // Converter para ms
    }

    // Calcular prioridade final
    Priority = DistanceFactor * ImportanceFactor * PerformanceFactor;

    return FMath::Clamp(Priority, 0.0f, 10.0f);
}

void AAURACRONPCGPerformanceManager::CleanupInvalidElements()
{
    // Limpar elementos inválidos usando APIs modernas do UE 5.6
    int32 RemovedCount = 0;

    // Iterar de trás para frente para remover elementos com segurança
    for (int32 i = RegisteredElements.Num() - 1; i >= 0; --i)
    {
        FAURACRONPCGElementPerformanceInfo& ElementInfo = RegisteredElements[i];

        // Verificar se elemento ainda é válido
        if (!ElementInfo.Actor.IsValid() || !IsValid(ElementInfo.Actor.Get()))
        {
            RegisteredElements.RemoveAt(i);
            RemovedCount++;
        }
        else if (!ElementInfo.Actor.Get()->IsValidLowLevel())
        {
            RegisteredElements.RemoveAt(i);
            RemovedCount++;
        }
    }

    if (RemovedCount > 0)
    {
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPerformanceManager::CleanupInvalidElements - Removed {0} invalid elements", RemovedCount);
    }
}

void AAURACRONPCGPerformanceManager::ApplyQualitySettings()
{
    // Aplicar configurações de qualidade usando APIs modernas do UE 5.6
    if (!CVarPCGPerformanceEnabled.GetValueOnGameThread())
    {
        return;
    }

    // Obter configurações de qualidade do sistema
    int32 QualityLevel = 2; // Padrão: Alta qualidade

    // Tentar obter do console variable
    static const auto* CVarQualityLevel = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ViewDistanceQuality"));
    if (CVarQualityLevel)
    {
        QualityLevel = CVarQualityLevel->GetInt();
    }

    // Aplicar configurações baseadas na qualidade
    switch (QualityLevel)
    {
        case 0: // Baixa qualidade
            CVarPCGLODDistance.AsVariable()->Set(1000.0f);
            CVarPCGCullingDistance.AsVariable()->Set(2000.0f);
            CVarPCGTargetFPS.AsVariable()->Set(30.0f);
            break;

        case 1: // Qualidade média
            CVarPCGLODDistance.AsVariable()->Set(1500.0f);
            CVarPCGCullingDistance.AsVariable()->Set(3000.0f);
            CVarPCGTargetFPS.AsVariable()->Set(45.0f);
            break;

        case 2: // Alta qualidade
            CVarPCGLODDistance.AsVariable()->Set(2000.0f);
            CVarPCGCullingDistance.AsVariable()->Set(4000.0f);
            CVarPCGTargetFPS.AsVariable()->Set(60.0f);
            break;

        default: // Qualidade épica
            CVarPCGLODDistance.AsVariable()->Set(3000.0f);
            CVarPCGCullingDistance.AsVariable()->Set(6000.0f);
            CVarPCGTargetFPS.AsVariable()->Set(120.0f);
            break;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPerformanceManager::ApplyQualitySettings - Applied quality level {0} settings", QualityLevel);
}

float AAURACRONPCGPerformanceManager::CalculateEffectiveDistance(const FAURACRONPCGElementPerformanceInfo& ElementInfo)
{
    // Calcular distância efetiva considerando importância usando algoritmos modernos
    float BaseDistance = ElementInfo.DistanceToViewer;

    // Fator de importância (elementos importantes parecem "mais próximos") - Alinhado com AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md
    float ImportanceFactor = 1.0f;
    if (ElementInfo.Actor.IsValid() && IsValid(ElementInfo.Actor.Get()))
    {
        if (Cast<AAURACRONPCGIsland>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 0.5f; // Ilha Central Auracron - máxima prioridade visual
        }
        else if (Cast<AAURACRONPCGPrismalFlow>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 0.7f; // Fluxo Prismal - espinha dorsal estratégica
        }
        else if (Cast<AAURACRONPCGTrail>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 0.75f; // Trilhos Solar/Axis/Lunar - navegação crítica
        }
        else if (Cast<AAURACRONPCGEnvironment>(ElementInfo.Actor.Get()))
        {
            ImportanceFactor = 0.8f; // Ambientes alternantes (Radiante/Zephyr/Purgatório)
        }
    }

    return BaseDistance * ImportanceFactor;
}

// ========================================
// IMPLEMENTAÇÃO DA FUNÇÃO FALTANTE - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGPerformanceManager::CollectPerformanceMetrics()
{
    // Coletar métricas de performance usando APIs modernas do UE 5.6
    if (!CVarPCGPerformanceEnabled.GetValueOnGameThread())
    {
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::CollectPerformanceMetrics - Collecting performance metrics");

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Coletar métricas básicas de FPS
    float CurrentDeltaTime = World->GetDeltaSeconds();
    float CurrentFPS = (CurrentDeltaTime > 0.0f) ? (1.0f / CurrentDeltaTime) : 0.0f;

    // Adicionar ao histórico de FPS para suavização
    FPSHistory.Add(CurrentFPS);
    if (FPSHistory.Num() > 60) // Manter apenas os últimos 60 frames
    {
        FPSHistory.RemoveAt(0);
    }

    // Calcular FPS médio
    float AverageFPS = 0.0f;
    if (FPSHistory.Num() > 0)
    {
        for (float FPS : FPSHistory)
        {
            AverageFPS += FPS;
        }
        AverageFPS /= FPSHistory.Num();
    }

    // Atualizar métricas atuais
    CurrentMetrics.AverageFPS = AverageFPS;
    CurrentMetrics.MinFPS = FPSHistory.Num() > 0 ? *MinElement(FPSHistory.GetData(), FPSHistory.GetData() + FPSHistory.Num()) : 0.0f;
    CurrentMetrics.MaxFPS = FPSHistory.Num() > 0 ? *MaxElement(FPSHistory.GetData(), FPSHistory.GetData() + FPSHistory.Num()) : 0.0f;

    // Coletar métricas de memória usando APIs modernas
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    CurrentMetrics.UsedMemoryMB = static_cast<float>(MemoryStats.UsedPhysical) / (1024.0f * 1024.0f);
    CurrentMetrics.AvailableMemoryMB = static_cast<float>(MemoryStats.AvailablePhysical) / (1024.0f * 1024.0f);

    // Coletar métricas de renderização usando APIs modernas
    if (UEngine* Engine = GEngine)
    {
        // Obter estatísticas de renderização usando API moderna do UE 5.6
        if (UGameViewportClient* ViewportClient = Engine->GameViewport)
        {
            if (FViewport* Viewport = ViewportClient->Viewport)
            {
                // Coletar informações de viewport
                FIntPoint ViewportSize = Viewport->GetSizeXY();
                CurrentMetrics.RenderTargetWidth = ViewportSize.X;
                CurrentMetrics.RenderTargetHeight = ViewportSize.Y;
            }
        }
    }

    // Coletar métricas específicas de PCG
    int32 ActivePCGElements = 0;
    int32 VisiblePCGElements = 0;
    float TotalPCGMemoryUsage = 0.0f;

    for (const FAURACRONPCGElementPerformanceInfo& ElementInfo : RegisteredElements)
    {
        if (ElementInfo.Actor.IsValid())
        {
            ActivePCGElements++;

            if (!ElementInfo.bIsOccluded && ElementInfo.bIsInFrustum)
            {
                VisiblePCGElements++;
            }

            // Estimar uso de memória baseado no LOD e distância
            float ElementMemoryUsage = 1.0f; // Base memory usage in MB
            switch (ElementInfo.CurrentLOD)
            {
                case EAURACRONPCGLODLevel::LOD0_Highest:
                    ElementMemoryUsage *= 4.0f;
                    break;
                case EAURACRONPCGLODLevel::LOD1_High:
                    ElementMemoryUsage *= 2.5f;
                    break;
                case EAURACRONPCGLODLevel::LOD2_Medium:
                    ElementMemoryUsage *= 1.5f;
                    break;
                case EAURACRONPCGLODLevel::LOD3_Low:
                    ElementMemoryUsage *= 0.8f;
                    break;
                case EAURACRONPCGLODLevel::LOD4_Culled:
                    ElementMemoryUsage = 0.1f;
                    break;
            }

            TotalPCGMemoryUsage += ElementMemoryUsage;
        }
    }

    // Atualizar métricas de PCG
    CurrentMetrics.ActivePCGElements = ActivePCGElements;
    CurrentMetrics.VisiblePCGElements = VisiblePCGElements;
    CurrentMetrics.PCGMemoryUsageMB = TotalPCGMemoryUsage;

    // Calcular métricas de eficiência
    CurrentMetrics.PCGEfficiencyRatio = (ActivePCGElements > 0) ?
        (static_cast<float>(VisiblePCGElements) / static_cast<float>(ActivePCGElements)) : 1.0f;

    // Coletar métricas de threading usando APIs modernas
    CurrentMetrics.GameThreadTime = FPlatformTime::Seconds() - World->GetTimeSeconds();

    // Coletar estatísticas de draw calls (estimativa)
    CurrentMetrics.EstimatedDrawCalls = VisiblePCGElements * 3; // Estimativa baseada em elementos visíveis

    // Coletar métricas de distância média dos elementos
    float TotalDistance = 0.0f;
    int32 ValidDistances = 0;

    for (const FAURACRONPCGElementPerformanceInfo& ElementInfo : RegisteredElements)
    {
        if (ElementInfo.Actor.IsValid() && ElementInfo.DistanceToViewer > 0.0f)
        {
            TotalDistance += ElementInfo.DistanceToViewer;
            ValidDistances++;
        }
    }

    CurrentMetrics.AverageElementDistance = (ValidDistances > 0) ? (TotalDistance / ValidDistances) : 0.0f;

    // Log métricas se necessário
    if (CVarPCGPerformanceEnabled.GetValueOnGameThread())
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::CollectPerformanceMetrics - FPS: {0} (Avg: {1}), Memory: {2} MB, PCG Elements: {3}/{4}",
               CurrentFPS, AverageFPS, CurrentMetrics.UsedMemoryMB, VisiblePCGElements, ActivePCGElements);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPerformanceManager::CollectPerformanceMetrics - Performance metrics collection completed");
}

// ========================================
// IMPLEMENTAÇÕES DE DETECÇÃO DE DISPOSITIVOS - FASE 3
// ========================================

void AAURACRONPCGPerformanceManager::InitializeDeviceProfiles()
{
    // Configurar perfil para Entry devices (baixa performance)
    FAURACRONDevicePerformanceProfile EntryProfile;
    EntryProfile.DeviceType = EAURACRONDeviceType::Entry;
    EntryProfile.QualityMultiplier = 0.5f;
    EntryProfile.MaxRenderDistance = 2500.0f;
    EntryProfile.MaxVisibleElements = 75;
    EntryProfile.DefaultLODLevel = EAURACRONPCGLODLevel::LOD3_Low;
    EntryProfile.bUseAggressiveCulling = true;
    EntryProfile.PrismalFlowVolatilityMultiplier = 0.6f;
    EntryProfile.LaneIntersectionMultiplier = 0.5f;
    DeviceProfiles.Add(EAURACRONDeviceType::Entry, EntryProfile);

    // Configurar perfil para Mid devices (performance média)
    FAURACRONDevicePerformanceProfile MidProfile;
    MidProfile.DeviceType = EAURACRONDeviceType::Mid;
    MidProfile.QualityMultiplier = 1.0f;
    MidProfile.MaxRenderDistance = 4000.0f;
    MidProfile.MaxVisibleElements = 150;
    MidProfile.DefaultLODLevel = EAURACRONPCGLODLevel::LOD2_Medium;
    MidProfile.bUseAggressiveCulling = false;
    MidProfile.PrismalFlowVolatilityMultiplier = 1.0f;
    MidProfile.LaneIntersectionMultiplier = 1.0f;
    DeviceProfiles.Add(EAURACRONDeviceType::Mid, MidProfile);

    // Configurar perfil para High devices (alta performance)
    FAURACRONDevicePerformanceProfile HighProfile;
    HighProfile.DeviceType = EAURACRONDeviceType::High;
    HighProfile.QualityMultiplier = 1.5f;
    HighProfile.MaxRenderDistance = 6000.0f;
    HighProfile.MaxVisibleElements = 300;
    HighProfile.DefaultLODLevel = EAURACRONPCGLODLevel::LOD1_High;
    HighProfile.bUseAggressiveCulling = false;
    HighProfile.PrismalFlowVolatilityMultiplier = 1.4f;
    HighProfile.LaneIntersectionMultiplier = 1.5f;
    DeviceProfiles.Add(EAURACRONDeviceType::High, HighProfile);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPerformanceManager::InitializeDeviceProfiles - Initialized {0} device profiles", DeviceProfiles.Num());
}

EAURACRONDeviceType AAURACRONPCGPerformanceManager::DetectDeviceType()
{
    if (bDeviceDetected)
    {
        return CurrentDeviceType;
    }

    return DetectDeviceTypeInternal();
}

EAURACRONDeviceType AAURACRONPCGPerformanceManager::DetectDeviceTypeInternal()
{
    // Detectar tipo de dispositivo baseado em capacidades de hardware usando APIs modernas do UE 5.6
    
    // Obter informações de renderização
    // Usar API moderna do UE 5.6 para obter capacidades de renderização
    bool bSupportsHighEndFeatures = RHISupportsRayTracing(GMaxRHIShaderPlatform);
    bool bSupportsBindlessResources = RHISupportsBindlessResources(GMaxRHIShaderPlatform);
    int32 MaxTextureSize = GMaxTextureDimensions;
    
    // Obter informações de memória
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    float TotalMemoryGB = MemStats.TotalPhysical / (1024.0f * 1024.0f * 1024.0f);
    
    // Obter informações de GPU
    FString GPUBrand = GRHIAdapterName;
    
    // Critérios de detecção baseados na documentação da Fase 3
    int32 DeviceScore = 0;
    
    // Pontuação baseada na memória
    if (TotalMemoryGB >= 16.0f)
    {
        DeviceScore += 3; // High device
    }
    else if (TotalMemoryGB >= 8.0f)
    {
        DeviceScore += 2; // Mid device
    }
    else
    {
        DeviceScore += 1; // Entry device
    }
    
    // Pontuação baseada na GPU (heurística simples)
    if (GPUBrand.Contains(TEXT("RTX 40")) || GPUBrand.Contains(TEXT("RTX 30")) || GPUBrand.Contains(TEXT("RX 7")) || GPUBrand.Contains(TEXT("RX 6")))
    {
        DeviceScore += 3; // GPU moderna
    }
    else if (GPUBrand.Contains(TEXT("RTX 20")) || GPUBrand.Contains(TEXT("GTX 16")) || GPUBrand.Contains(TEXT("RX 5")))
    {
        DeviceScore += 2; // GPU média
    }
    else
    {
        DeviceScore += 1; // GPU antiga/integrada
    }
    
    // Pontuação baseada em capacidades de renderização
    if (bSupportsHighEndFeatures && MaxTextureSize >= 8192)
    {
        DeviceScore += 2;
    }
    else if (MaxTextureSize >= 4096)
    {
        DeviceScore += 1;
    }
    
    // Determinar tipo de dispositivo baseado na pontuação
    EAURACRONDeviceType DetectedType = EAURACRONDeviceType::Mid; // Padrão
    
    if (DeviceScore >= 7)
    {
        DetectedType = EAURACRONDeviceType::High;
    }
    else if (DeviceScore >= 4)
    {
        DetectedType = EAURACRONDeviceType::Mid;
    }
    else
    {
        DetectedType = EAURACRONDeviceType::Entry;
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPerformanceManager::DetectDeviceTypeInternal - Device score: {0}, Memory: {1}GB, GPU: {2}, Type: {3}",
           DeviceScore, TotalMemoryGB, GPUBrand, (int32)DetectedType);
    
    return DetectedType;
}

void AAURACRONPCGPerformanceManager::ApplyDeviceSpecificSettings()
{
    if (CurrentDeviceType == EAURACRONDeviceType::Unknown)
    {
        return;
    }
    
    // Configurar perfil atual baseado no tipo de dispositivo
    ConfigureDeviceProfile(CurrentDeviceType);
    
    // Aplicar configurações específicas
    switch (CurrentDeviceType)
    {
        case EAURACRONDeviceType::Entry:
            ApplyEntryDeviceSettings();
            break;
            
        case EAURACRONDeviceType::Mid:
            ApplyMidDeviceSettings();
            break;
            
        case EAURACRONDeviceType::High:
            ApplyHighDeviceSettings();
            break;
            
        default:
            ApplyMidDeviceSettings(); // Fallback para Mid
            break;
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPerformanceManager::ApplyDeviceSpecificSettings - Applied settings for device type {0}", (int32)CurrentDeviceType);
}

void AAURACRONPCGPerformanceManager::ConfigureDeviceProfile(EAURACRONDeviceType DeviceType)
{
    if (FAURACRONDevicePerformanceProfile* Profile = DeviceProfiles.Find(DeviceType))
    {
        CurrentDeviceProfile = *Profile;
    }
    else
    {
        // Usar perfil Mid como fallback
        if (FAURACRONDevicePerformanceProfile* MidProfile = DeviceProfiles.Find(EAURACRONDeviceType::Mid))
        {
            CurrentDeviceProfile = *MidProfile;
        }
    }
}

void AAURACRONPCGPerformanceManager::ApplyEntryDeviceSettings()
{
    // Configurações agressivas para Entry devices conforme AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md Fase 3
    // Entry Level: Apenas Planície Radiante ativa, outros ambientes como "preview zones"
    // Trilhos a 50% de poder, efeitos visuais adaptados ao hardware
    PerformanceConfig.MaxLODDistance = CurrentDeviceProfile.MaxRenderDistance;
    PerformanceConfig.CullingDistance = CurrentDeviceProfile.MaxRenderDistance * 0.8f;
    PerformanceConfig.MaxVisibleElements = CurrentDeviceProfile.MaxVisibleElements;
    PerformanceConfig.bEnableOcclusion = true;
    PerformanceConfig.bEnableFrustumCulling = true;
    
    // Configurar console variables para Entry devices
    CVarPCGMaxVisibleElements.AsVariable()->Set(CurrentDeviceProfile.MaxVisibleElements);
    CVarPCGLODDistance.AsVariable()->Set(CurrentDeviceProfile.MaxRenderDistance * 0.5f);
    CVarPCGCullingDistance.AsVariable()->Set(CurrentDeviceProfile.MaxRenderDistance);
    
    // Reduzir FPS alvo para Entry devices
    TargetFPS = 30.0f;
    FPSTolerance = 3.0f;
}

void AAURACRONPCGPerformanceManager::ApplyMidDeviceSettings()
{
    // Configurações balanceadas para Mid devices conforme AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md Fase 3
    // Mid-range: 2 ambientes simultâneos com transições simplificadas
    // Trilhos atingem poder baseado na capacidade do dispositivo
    PerformanceConfig.MaxLODDistance = CurrentDeviceProfile.MaxRenderDistance;
    PerformanceConfig.CullingDistance = CurrentDeviceProfile.MaxRenderDistance * 1.2f;
    PerformanceConfig.MaxVisibleElements = CurrentDeviceProfile.MaxVisibleElements;
    PerformanceConfig.bEnableOcclusion = true;
    PerformanceConfig.bEnableFrustumCulling = true;
    
    // Configurar console variables para Mid devices
    CVarPCGMaxVisibleElements.AsVariable()->Set(CurrentDeviceProfile.MaxVisibleElements);
    CVarPCGLODDistance.AsVariable()->Set(CurrentDeviceProfile.MaxRenderDistance * 0.6f);
    CVarPCGCullingDistance.AsVariable()->Set(CurrentDeviceProfile.MaxRenderDistance);
    
    // FPS alvo padrão para Mid devices
    TargetFPS = 60.0f;
    FPSTolerance = 5.0f;
}

void AAURACRONPCGPerformanceManager::ApplyHighDeviceSettings()
{
    // Configurações otimizadas para High devices conforme AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md Fase 3
    // High-end: Mudanças dramáticas de terreno completas, todos os trilhos convergem
    // Fluxo Prismal com volatilidade adaptada ao hardware, efeitos finais ajustados automaticamente
    PerformanceConfig.MaxLODDistance = CurrentDeviceProfile.MaxRenderDistance;
    PerformanceConfig.CullingDistance = CurrentDeviceProfile.MaxRenderDistance * 1.5f;
    PerformanceConfig.MaxVisibleElements = CurrentDeviceProfile.MaxVisibleElements;
    PerformanceConfig.bEnableOcclusion = false; // Menos culling agressivo para experiência visual completa
    PerformanceConfig.bEnableFrustumCulling = true;
    
    // Configurar console variables para High devices
    CVarPCGMaxVisibleElements.AsVariable()->Set(CurrentDeviceProfile.MaxVisibleElements);
    CVarPCGLODDistance.AsVariable()->Set(CurrentDeviceProfile.MaxRenderDistance * 0.8f);
    CVarPCGCullingDistance.AsVariable()->Set(CurrentDeviceProfile.MaxRenderDistance);
    
    // FPS alvo alto para High devices
    TargetFPS = 90.0f;
    FPSTolerance = 10.0f;
}

void AAURACRONPCGPerformanceManager::GetDeviceBasedQualitySettings(float& OutQualityMultiplier, int32& OutMaxElements, float& OutMaxDistance)
{
    OutQualityMultiplier = CurrentDeviceProfile.QualityMultiplier;
    OutMaxElements = CurrentDeviceProfile.MaxVisibleElements;
    OutMaxDistance = CurrentDeviceProfile.MaxRenderDistance;
}
